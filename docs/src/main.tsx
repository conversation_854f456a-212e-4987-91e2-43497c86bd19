import ReactDOM from "react-dom/client";
import { Theme<PERSON>rovider, useTheme } from "@nui/ui";

// Example app component
function App() {
  const {
    mode,
    variant,
    isDark,
    availableVariants,
    setMode,
    setVariant,
    toggleMode,
    cycleVariant,
  } = useTheme();

  return (
    <div className="min-h-screen p-8 bg-background text-foreground">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold">Theme System Demo</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Current Theme Info */}
          <div className="p-4 bg-card border rounded-lg">
            <h2 className="text-xl font-semibold mb-3">Current Theme</h2>
            <div className="space-y-2 text-sm">
              <div>
                Mode: <code className="bg-muted px-2 py-1 rounded">{mode}</code>
              </div>
              <div>
                Variant:{" "}
                <code className="bg-muted px-2 py-1 rounded">{variant}</code>
              </div>
              <div>
                Is Dark:{" "}
                <code className="bg-muted px-2 py-1 rounded">
                  {isDark.toString()}
                </code>
              </div>
              <div>
                Available:{" "}
                <code className="bg-muted px-2 py-1 rounded">
                  {availableVariants.join(", ")}
                </code>
              </div>
            </div>
          </div>

          {/* Theme Controls */}
          <div className="p-4 bg-card border rounded-lg">
            <h2 className="text-xl font-semibold mb-3">Theme Controls</h2>
            <div className="space-y-3">
              <button
                onClick={toggleMode}
                className="w-full px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
              >
                Toggle Mode ({mode})
              </button>

              <button
                onClick={cycleVariant}
                className="w-full px-4 py-2 bg-secondary text-secondary-foreground rounded hover:bg-secondary/90"
              >
                Cycle Variant ({variant})
              </button>

              <div className="grid grid-cols-3 gap-2">
                {["light", "dark", "auto"].map((m) => (
                  <button
                    key={m}
                    onClick={() => setMode(m as any)}
                    className={`px-3 py-2 text-sm rounded border ${
                      mode === m
                        ? "bg-primary text-primary-foreground"
                        : "bg-background hover:bg-muted"
                    }`}
                  >
                    {m}
                  </button>
                ))}
              </div>

              <div className="grid grid-cols-2 gap-2">
                {availableVariants.map((v) => (
                  <button
                    key={v}
                    onClick={() => setVariant(v)}
                    className={`px-3 py-2 text-sm rounded border ${
                      variant === v
                        ? "bg-primary text-primary-foreground"
                        : "bg-background hover:bg-muted"
                    }`}
                  >
                    {v}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Color Demonstration */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="p-4 bg-primary text-primary-foreground rounded-lg">
            <h3 className="font-semibold">Primary</h3>
            <p className="text-sm opacity-90">Primary colors</p>
          </div>
          <div className="p-4 bg-secondary text-secondary-foreground rounded-lg">
            <h3 className="font-semibold">Secondary</h3>
            <p className="text-sm opacity-90">Secondary colors</p>
          </div>
          <div className="p-4 bg-muted text-muted-foreground rounded-lg">
            <h3 className="font-semibold">Muted</h3>
            <p className="text-sm">Muted colors</p>
          </div>
          <div className="p-4 bg-accent text-accent-foreground rounded-lg">
            <h3 className="font-semibold">Accent</h3>
            <p className="text-sm opacity-90">Accent colors</p>
          </div>
        </div>

        {/* Destructive Example */}
        <div className="p-4 bg-destructive text-destructive-foreground rounded-lg">
          <h3 className="font-semibold">Destructive Action</h3>
          <p className="text-sm opacity-90">Used for dangerous actions</p>
        </div>
      </div>
    </div>
  );
}

// Different provider configurations
const configurations = [
  // Basic configuration
  {
    name: "Basic",
    component: (
      <ThemeProvider availableVariants={["default", "purple", "blue"]}>
        <App />
      </ThemeProvider>
    ),
  },

  // With default theme
  {
    name: "With Default",
    component: (
      <ThemeProvider
        availableVariants={["default", "purple", "blue", "green"]}
        defaultTheme={{ mode: "dark", variant: "purple" }}
      >
        <App />
      </ThemeProvider>
    ),
  },

  // Forced theme (no user control)
  {
    name: "Forced Theme",
    component: (
      <ThemeProvider
        availableVariants={["default", "purple"]}
        forcedTheme={{ mode: "light", variant: "purple" }}
      >
        <App />
      </ThemeProvider>
    ),
  },

  // Data attribute instead of classes
  {
    name: "Data Attribute",
    component: (
      <ThemeProvider
        availableVariants={["default", "blue", "green"]}
        attribute="data-theme"
      >
        <App />
      </ThemeProvider>
    ),
  },

  // No storage persistence
  {
    name: "No Storage",
    component: (
      <ThemeProvider
        availableVariants={["default", "purple", "blue"]}
        disableStorage={true}
        defaultTheme={{ mode: "light", variant: "blue" }}
      >
        <App />
      </ThemeProvider>
    ),
  },
];

// Render the first configuration (you can change the index to test others)
const configIndex = 0;
ReactDOM.createRoot(document.getElementById("root")!).render(
  configurations[configIndex].component
);
