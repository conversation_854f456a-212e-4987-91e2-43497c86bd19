<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Test</title>
    <style>
        /* Import the theme CSS */
        @import url('./packages/ui/src/theme.css');
        
        body {
            font-family: system-ui, sans-serif;
            margin: 0;
            padding: 20px;
            transition: background-color 0.3s, color 0.3s;
        }
        
        .theme-test {
            padding: 20px;
            margin: 10px 0;
            border: 1px solid var(--color-border);
            border-radius: 8px;
            background: var(--color-background);
            color: var(--color-foreground);
        }
        
        .controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 8px 16px;
            border: 1px solid var(--color-border);
            border-radius: 4px;
            background: var(--color-secondary);
            color: var(--color-secondary-foreground);
            cursor: pointer;
        }
        
        button:hover {
            background: var(--color-muted);
        }
        
        button.active {
            background: var(--color-primary);
            color: var(--color-primary-foreground);
        }
        
        .info {
            background: var(--color-muted);
            color: var(--color-muted-foreground);
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Theme System Test</h1>
    
    <div class="info">
        <strong>Current Theme:</strong> <span id="current-theme">default light</span><br>
        <strong>HTML Classes:</strong> <span id="html-classes"></span>
    </div>
    
    <div class="controls">
        <h3>Mode:</h3>
        <button onclick="setMode('light')" id="mode-light">Light</button>
        <button onclick="setMode('dark')" id="mode-dark">Dark</button>
        <button onclick="setMode('auto')" id="mode-auto">Auto</button>
    </div>
    
    <div class="controls">
        <h3>Variant:</h3>
        <button onclick="setVariant('default')" id="variant-default">Default</button>
        <button onclick="setVariant('gray')" id="variant-gray">Gray</button>
        <button onclick="setVariant('slate')" id="variant-slate">Slate</button>
        <button onclick="setVariant('purple')" id="variant-purple">Purple</button>
        <button onclick="setVariant('blue')" id="variant-blue">Blue</button>
    </div>
    
    <div class="theme-test">
        <h2>Sample Content</h2>
        <p>This is a test of the theme system. The background, text, and border colors should change when you switch themes.</p>
        <button>Sample Button</button>
    </div>
    
    <script>
        let currentMode = 'light';
        let currentVariant = 'default';
        
        function updateDisplay() {
            document.getElementById('current-theme').textContent = `${currentVariant} ${currentMode}`;
            document.getElementById('html-classes').textContent = document.documentElement.className;
            
            // Update active buttons
            document.querySelectorAll('button[id^="mode-"]').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('button[id^="variant-"]').forEach(btn => btn.classList.remove('active'));
            
            document.getElementById(`mode-${currentMode}`).classList.add('active');
            document.getElementById(`variant-${currentVariant}`).classList.add('active');
        }
        
        function setMode(mode) {
            currentMode = mode;
            applyTheme();
        }
        
        function setVariant(variant) {
            currentVariant = variant;
            applyTheme();
        }
        
        function applyTheme() {
            const html = document.documentElement;
            
            // Remove existing classes
            html.classList.remove('light', 'dark');
            html.classList.remove('theme-default', 'theme-gray', 'theme-slate', 'theme-purple', 'theme-blue');
            
            // Apply mode
            const resolvedMode = currentMode === 'auto' 
                ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light')
                : currentMode;
            html.classList.add(resolvedMode);
            
            // Apply variant (only if not default)
            if (currentVariant !== 'default') {
                html.classList.add(`theme-${currentVariant}`);
            }
            
            updateDisplay();
        }
        
        // Initialize
        applyTheme();
        
        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
            if (currentMode === 'auto') {
                applyTheme();
            }
        });
    </script>
</body>
</html>
